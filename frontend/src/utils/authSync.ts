/**
 * Authentication State Synchronization Utilities
 * Provides functions to manually sync Redux state with authentication tokens
 */

import { store } from '../store'
import { syncAuthState, syncTokenState } from '../store/slices/authSlice'
import { authService } from '../services/authService'

/**
 * Manually sync Redux authentication state with current tokens
 * Useful for emergency login, testing, or when tokens are set manually
 *
 * @returns Promise<boolean> - true if sync successful, false otherwise
 */
export async function syncReduxAuthState(): Promise<boolean> {
  try {
    console.log('🔄 Syncing Redux auth state with current tokens...')

    // Check current Redux state
    const currentState = store.getState().auth
    console.log('🔍 Current Redux auth state:', {
      isAuthenticated: currentState.isAuthenticated,
      hasUser: !!currentState.user,
      username: currentState.user?.username
    })

    // Check available tokens
    const hasLocalToken = !!localStorage.getItem('access_token')
    const hasCookies = document.cookie.includes('access_token')

    console.log('🔍 Available authentication:', {
      hasLocalToken,
      hasCookies
    })

    if (!hasLocalToken && !hasCookies) {
      console.log('⚠️ No authentication tokens found - cannot sync')
      return false
    }

    // Dispatch the sync action
    const result = await store.dispatch(syncAuthState())

    if (syncAuthState.fulfilled.match(result)) {
      console.log('✅ Redux auth state synced successfully!')
      console.log('👤 User:', result.payload.username)
      console.log('🔐 Authentication method:', hasLocalToken ? 'localStorage' : 'cookies')
      return true
    } else {
      console.log('❌ Redux auth state sync failed:', result.payload)
      return false
    }
  } catch (error) {
    console.error('❌ Error syncing Redux auth state:', error)
    return false
  }
}

/**
 * Check if user is authenticated (either via cookies or localStorage)
 * @returns Promise<boolean>
 */
export async function checkAuthStatus(): Promise<boolean> {
  try {
    const result = await syncReduxAuthState()
    return result
  } catch (error) {
    console.error('Error checking auth status:', error)
    return false
  }
}

/**
 * Force sync Redux state when tokens are manually set
 * This is specifically for cases where tokens are set outside the normal login flow
 *
 * @param token - Optional token to verify before syncing
 * @returns Promise<boolean> - true if sync successful, false otherwise
 */
export async function forceTokenSync(token?: string): Promise<boolean> {
  try {
    console.log('🔧 Force syncing manually set tokens...')

    // If token provided, verify it first
    if (token) {
      console.log('🔍 Verifying provided token...')
      // Set the token in localStorage for verification
      localStorage.setItem('access_token', token)
    }

    // Check if we have any tokens to sync
    const hasLocalToken = !!localStorage.getItem('access_token')
    const hasCookies = document.cookie.includes('access_token')

    if (!hasLocalToken && !hasCookies) {
      console.log('❌ No tokens available for force sync')
      return false
    }

    // Perform the sync
    const result = await syncReduxAuthState()

    if (result) {
      console.log('✅ Force token sync successful!')
    } else {
      console.log('❌ Force token sync failed')
    }

    return result
  } catch (error) {
    console.error('❌ Error in force token sync:', error)
    return false
  }
}

/**
 * Global function for browser console access
 * Makes it easy to sync auth state from browser console
 */
if (typeof window !== 'undefined') {
  // Make functions available globally for debugging
  (window as any).syncAuth = syncReduxAuthState
  (window as any).checkAuth = checkAuthStatus
  (window as any).forceSync = forceTokenSync
  (window as any).manualAuth = manualAuthenticate
  (window as any).syncUser = syncUserToRedux

  console.log('🔧 Auth sync utilities available:')
  console.log('  - window.syncAuth() - Sync Redux state with current tokens')
  console.log('  - window.checkAuth() - Check current authentication status')
  console.log('  - window.forceSync(token?) - Force sync manually set tokens')
  console.log('  - window.manualAuth(token, refresh?) - Complete manual auth flow')
  console.log('  - window.syncUser(user) - Directly sync user object to Redux')
}

/**
 * Directly sync user data to Redux state
 * Use this when you have a valid user object and want to bypass API calls
 *
 * @param user - User object to sync to Redux
 */
export function syncUserToRedux(user: any): void {
  try {
    console.log('🔧 Directly syncing user to Redux state:', user.username)

    store.dispatch(syncTokenState({ user }))

    console.log('✅ User synced directly to Redux state')
  } catch (error) {
    console.error('❌ Error syncing user to Redux:', error)
  }
}

/**
 * Complete manual authentication flow
 * Sets tokens and syncs user data to Redux
 *
 * @param accessToken - Access token to set
 * @param refreshToken - Optional refresh token
 * @returns Promise<boolean> - true if successful
 */
export async function manualAuthenticate(accessToken: string, refreshToken?: string): Promise<boolean> {
  try {
    console.log('🔧 Starting manual authentication flow...')

    // Set tokens in localStorage
    localStorage.setItem('access_token', accessToken)
    if (refreshToken) {
      localStorage.setItem('refresh_token', refreshToken)
    }

    console.log('✅ Tokens set in localStorage')

    // Verify token and get user data
    const user = await authService.verifyToken()

    if (user) {
      // Sync user to Redux
      syncUserToRedux(user)
      console.log('✅ Manual authentication completed successfully')
      return true
    } else {
      console.log('❌ Token verification failed')
      return false
    }
  } catch (error) {
    console.error('❌ Manual authentication failed:', error)
    return false
  }
}

export default {
  syncReduxAuthState,
  checkAuthStatus,
  forceTokenSync,
  syncUserToRedux,
  manualAuthenticate
}
