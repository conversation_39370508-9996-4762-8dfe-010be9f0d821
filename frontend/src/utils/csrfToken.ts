/**
 * SECURITY FIX: Enhanced CSRF Token Utility & XSS Protection
 * Handles CSRF token retrieval, management, and XSS prevention for secure API requests
 */

import { log } from './logger'

// CSRF token cache to avoid multiple cookie reads
let csrfTokenCache: string | null = null
let csrfTokenExpiry: number = 0

/**
 * Get CSRF token from cookie
 * SECURITY FIX: Implements proper CSRF protection for SPA
 */
export function getCSRFToken(): string | null {
  // Check cache first (valid for 5 minutes)
  const now = Date.now()
  if (csrfTokenCache && now < csrfTokenExpiry) {
    return csrfTokenCache
  }

  // Get token from cookie
  const token = getCookie('csrftoken')
  
  if (token) {
    // Cache the token for 5 minutes
    csrfTokenCache = token
    csrfTokenExpiry = now + (5 * 60 * 1000)
  }
  
  return token
}

/**
 * Get cookie value by name
 */
function getCookie(name: string): string | null {
  if (typeof document === 'undefined') {
    return null // SSR safety
  }

  const value = `; ${document.cookie}`
  const parts = value.split(`; ${name}=`)
  
  if (parts.length === 2) {
    const cookieValue = parts.pop()?.split(';').shift()
    return cookieValue || null
  }
  
  return null
}

/**
 * SECURITY FIX: Fetch CSRF token from secure endpoint
 * Works with httpOnly CSRF cookies for enhanced security
 */
// Track if we're currently fetching to prevent multiple simultaneous requests
let isFetching = false

// Track logout state to prevent CSRF requests during logout
let isLoggingOut = false

export function setLogoutState(loggingOut: boolean) {
  isLoggingOut = loggingOut
}

export async function ensureCSRFToken(): Promise<string | null> {
  // Don't fetch CSRF token during logout
  if (isLoggingOut) {
    return null
  }

  let token = getCSRFToken()

  if (!token && !isFetching) {
    isFetching = true
    try {
      // Make a GET request to secure CSRF endpoint
      const response = await fetch('/api/auth/csrf/', {
        method: 'GET',
        credentials: 'include', // Include httpOnly cookies
        headers: {
          'Accept': 'application/json',
        }
      })

      if (response.ok) {
        const data = await response.json()
        // SECURITY FIX: Get token from response body since cookie is httpOnly
        if (data.csrfToken) {
          // Cache the token temporarily (it's also in httpOnly cookie)
          csrfTokenCache = data.csrfToken
          csrfTokenExpiry = Date.now() + (5 * 60 * 1000) // 5 minutes
          return data.csrfToken
        }
        // Fallback: try to get from cookie (for backward compatibility)
        token = getCSRFToken()
      }
    } catch (error) {
      console.error('Failed to fetch CSRF token:', error)
    } finally {
      isFetching = false
    }
  }

  return token
}

/**
 * Clear CSRF token cache (useful for logout)
 */
export function clearCSRFTokenCache(): void {
  csrfTokenCache = null
  csrfTokenExpiry = 0
}

/**
 * Get headers with CSRF token for API requests
 * SECURITY FIX: Automatically includes CSRF token in headers
 */
export function getCSRFHeaders(): HeadersInit {
  // Don't include CSRF headers during logout
  if (isLoggingOut) {
    return {}
  }

  const token = getCSRFToken()
  const headers: HeadersInit = {}

  if (token) {
    headers['X-CSRFToken'] = token
  }

  return headers
}

/**
 * Enhanced fetch wrapper with automatic CSRF token handling
 * SECURITY FIX: Automatically includes CSRF tokens in all requests
 */
export async function csrfFetch(
  url: string, 
  options: RequestInit = {}
): Promise<Response> {
  // Ensure we have a CSRF token for non-GET requests
  const method = options.method?.toUpperCase() || 'GET'
  const needsCSRF = !['GET', 'HEAD', 'OPTIONS', 'TRACE'].includes(method)
  
  if (needsCSRF) {
    await ensureCSRFToken()
  }
  
  // Merge CSRF headers with existing headers
  const csrfHeaders = getCSRFHeaders()
  const headers = {
    ...csrfHeaders,
    ...options.headers
  }
  
  // Make the request with CSRF protection
  return fetch(url, {
    ...options,
    headers,
    credentials: 'include' // Always include cookies
  })
}

/**
 * CSRF-protected form submission utility
 * SECURITY FIX: Ensures forms include CSRF tokens
 */
export async function submitFormWithCSRF(
  url: string,
  formData: FormData | Record<string, any>,
  options: RequestInit = {}
): Promise<Response> {
  // Ensure CSRF token is available
  await ensureCSRFToken()
  
  let body: BodyInit
  const headers: HeadersInit = {
    ...getCSRFHeaders(),
    ...options.headers
  }
  
  if (formData instanceof FormData) {
    // For FormData, don't set Content-Type (browser will set it with boundary)
    body = formData
    delete (headers as any)['Content-Type']
  } else {
    // For JSON data
    headers['Content-Type'] = 'application/json'
    body = JSON.stringify(formData)
  }
  
  return fetch(url, {
    method: 'POST',
    ...options,
    headers,
    body,
    credentials: 'include'
  })
}

/**
 * Initialize CSRF protection on app startup
 * SECURITY FIX: Ensures CSRF token is available from the start
 */
export async function initializeCSRFProtection(): Promise<void> {
  try {
    await ensureCSRFToken()
    console.log('✅ CSRF protection initialized')
  } catch (error) {
    console.error('❌ Failed to initialize CSRF protection:', error)
  }
}

/**
 * Check if CSRF protection is working
 * Utility for debugging and testing
 */
export function checkCSRFProtection(): {
  hasToken: boolean
  tokenValue: string | null
  cacheStatus: 'valid' | 'expired' | 'empty'
} {
  const token = getCSRFToken()
  const now = Date.now()
  
  let cacheStatus: 'valid' | 'expired' | 'empty' = 'empty'
  if (csrfTokenCache) {
    cacheStatus = now < csrfTokenExpiry ? 'valid' : 'expired'
  }
  
  return {
    hasToken: !!token,
    tokenValue: token,
    cacheStatus
  }
}

/**
 * XSS Protection Functions
 */

/**
 * Sanitize HTML content to prevent XSS attacks
 * Note: In React, use proper escaping in JSX instead
 */
export function sanitizeHtml(html: string): string {
  // Simple text escaping - use React's built-in escaping instead
  return html.replace(/[<>&"']/g, (match) => {
    const escapeMap: Record<string, string> = {
      '<': '&lt;',
      '>': '&gt;',
      '&': '&amp;',
      '"': '&quot;',
      "'": '&#x27;'
    }
    return escapeMap[match]
  })
}

/**
 * Escape HTML entities
 */
export function escapeHtml(text: string): string {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

/**
 * Validate and sanitize URL to prevent javascript: and data: URLs
 */
export function sanitizeUrl(url: string): string {
  const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:']

  try {
    const urlObj = new URL(url, window.location.origin)

    if (!allowedProtocols.includes(urlObj.protocol)) {
      log.warn('security', `Blocked potentially dangerous URL protocol: ${urlObj.protocol}`)
      return '#'
    }

    return urlObj.href
  } catch {
    log.warn('security', `Invalid URL blocked: ${url}`)
    return '#'
  }
}

/**
 * Note: DOM manipulation removed - use React's dangerouslySetInnerHTML with proper sanitization instead
 */

/**
 * Monitor for XSS attempts in form inputs
 */
export function monitorXSSAttempts(): void {
  document.addEventListener('input', (event) => {
    const target = event.target as HTMLInputElement | HTMLTextAreaElement
    if (!target || !target.value) return

    const value = target.value.toLowerCase()
    const xssPatterns = [
      '<script',
      'javascript:',
      'vbscript:',
      'onload=',
      'onerror=',
      'onclick='
    ]

    for (const pattern of xssPatterns) {
      if (value.includes(pattern)) {
        log.warn('security', `Potential XSS attempt detected in input: ${pattern}`)

        // Optional: Clear the input or show warning
        if (target.dataset.strictSecurity === 'true') {
          target.value = target.value.replace(new RegExp(pattern, 'gi'), '')
        }
        break
      }
    }
  })
}

export default {
  getCSRFToken,
  ensureCSRFToken,
  clearCSRFTokenCache,
  getCSRFHeaders,
  csrfFetch,
  submitFormWithCSRF,
  initializeCSRFProtection,
  checkCSRFProtection,
  setLogoutState,
  // XSS Protection
  sanitizeHtml,
  escapeHtml,
  sanitizeUrl,
  safeSetInnerHTML,
  monitorXSSAttempts
}
