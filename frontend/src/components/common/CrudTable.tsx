/**
 * Generic CRUD Table Component
 * Reusable table with sorting, filtering, and actions for all entities
 */

import React, { useState, useMemo, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Search,
  Filter,
  SortAsc,
  SortDesc,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
  X,
  Plus,
  RefreshCw,
  Download,
  Upload,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  FileText,
  FileSpreadsheet,
  FileImage
} from 'lucide-react'
import { UnifiedExportButton } from './UnifiedExportButton'
import { LoadingOverlay, EnhancedSpinner } from '@/components/ui/enhanced-loading'
import { useUXFeedback } from '@/utils/uxFeedbackManager'
// Note: Mobile optimization utility removed - using CSS media queries instead
import { MobileTable, TouchButton, CollapsibleSection } from '@/components/ui/mobile-responsive'

export interface TableColumn<T = Record<string, unknown>> {
  key: string
  label: string
  sortable?: boolean
  filterable?: boolean
  render?: (item: T) => React.ReactNode
  width?: string
  align?: 'left' | 'center' | 'right'
}

export interface TableAction<T = Record<string, unknown>> {
  label: string
  icon: React.ComponentType<{ className?: string }>
  onClick: (item: T) => void
  variant?: 'default' | 'destructive' | 'outline' | 'ghost'
  className?: string
  show?: (item: T) => boolean
}

export interface FilterOption {
  key: string
  label: string
  options: { value: string; label: string }[]
}

export interface CrudTableProps<T = Record<string, unknown>> {
  title: string
  data: T[]
  columns: TableColumn<T>[]
  actions?: TableAction<T>[]
  filters?: FilterOption[]
  loading?: boolean
  searchPlaceholder?: string
  language: 'ar' | 'en'

  // CRUD operations
  onCreate?: () => void
  onRefresh?: () => void
  onExport?: (format?: 'csv' | 'excel' | 'pdf') => void
  onImport?: (file: File) => void

  // UI Control props for role-based restrictions
  showCreateButton?: boolean
  showExportButton?: boolean
  showImportButton?: boolean
  createButtonText?: string

  // Pagination
  currentPage?: number
  totalPages?: number
  pageSize?: number
  total?: number
  onPageChange?: (page: number) => void
  onPageSizeChange?: (pageSize: number) => void

  // Search and filter
  searchQuery?: string
  onSearchChange?: (query: string) => void
  activeFilters?: Record<string, string>
  onFilterChange?: (filters: Record<string, string>) => void

  // Sorting
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  onSortChange?: (sortBy: string, sortOrder: 'asc' | 'desc') => void

  // Selection
  selectable?: boolean
  selectedItems?: T[]
  onSelectionChange?: (items: T[]) => void

  // Bulk actions
  bulkActions?: TableAction[]
}

const translations = {
  ar: {
    search: 'بحث...',
    filter: 'تصفية',
    create: 'إنشاء جديد',
    refresh: 'تحديث',
    export: 'تصدير',
    import: 'استيراد',
    actions: 'الإجراءات',
    noData: 'لا توجد بيانات',
    loading: 'جاري التحميل...',
    page: 'صفحة',
    of: 'من',
    items: 'عنصر',
    selected: 'محدد',
    selectAll: 'تحديد الكل',
    clearSelection: 'إلغاء التحديد',
    bulkActions: 'إجراءات مجمعة',
    previous: 'السابق',
    next: 'التالي'
  },
  en: {
    search: 'Search...',
    filter: 'Filter',
    create: 'Create New',
    refresh: 'Refresh',
    export: 'Export',
    import: 'Import',
    actions: 'Actions',
    noData: 'No data available',
    loading: 'Loading...',
    page: 'Page',
    of: 'of',
    items: 'items',
    selected: 'selected',
    selectAll: 'Select All',
    clearSelection: 'Clear Selection',
    bulkActions: 'Bulk Actions',
    previous: 'Previous',
    next: 'Next'
  }
}

export default function CrudTable<T = Record<string, unknown>>({
  title,
  data,
  columns,
  actions = [],
  filters = [],
  loading = false,
  searchPlaceholder,
  language,
  onCreate,
  onRefresh,
  onExport,
  onImport,
  currentPage = 1,
  totalPages = 1,
  pageSize = 20,
  total = 0,
  onPageChange,
  onPageSizeChange,
  searchQuery = '',
  onSearchChange,
  activeFilters = {},
  onFilterChange,
  sortBy,
  sortOrder,
  onSortChange,
  selectable = false,
  selectedItems = [],
  onSelectionChange,
  bulkActions = [],
  showCreateButton = true,
  showExportButton = true,
  showImportButton = true,
  createButtonText
}: CrudTableProps<T>) {
  const [showFilters, setShowFilters] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // UX FIX: Enhanced feedback system
  const uxFeedback = useUXFeedback(language)

  // MOBILE FIX: Mobile optimization using CSS media queries
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)

  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth
      setIsMobile(width < 768)
      setIsTablet(width >= 768 && width < 1024)
    }

    checkDevice()
    window.addEventListener('resize', checkDevice)
    return () => window.removeEventListener('resize', checkDevice)
  }, [])

  // FIXED: Memoize expensive computations
  const memoizedData = useMemo(() => data, [data])
  const memoizedColumns = useMemo(() => columns, [columns])
  const memoizedActions = useMemo(() => actions, [actions])

  // Handle sort
  const handleSort = (columnKey: string) => {
    if (!onSortChange) return

    const newSortOrder = sortBy === columnKey && sortOrder === 'asc' ? 'desc' : 'asc'
    onSortChange(columnKey, newSortOrder)
  }

  // Handle selection
  const handleSelectAll = () => {
    if (!onSelectionChange) return

    if (selectedItems.length === memoizedData.length) {
      onSelectionChange([])
    } else {
      onSelectionChange(memoizedData)
    }
  }

  const handleSelectItem = (item: T) => {
    if (!onSelectionChange) return

    const isSelected = selectedItems.some(selected => (selected as Record<string, unknown>).id === (item as Record<string, unknown>).id)
    if (isSelected) {
      onSelectionChange(selectedItems.filter(selected => (selected as Record<string, unknown>).id !== (item as Record<string, unknown>).id))
    } else {
      onSelectionChange([...selectedItems, item])
    }
  }

  // Handle file import
  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && onImport) {
      onImport(file)
    }
    // Reset input
    event.target.value = ''
  }

  return (
    <Card className="glass-card border-white/20">

      <CardHeader>
        {/* MOBILE FIX: Enhanced mobile-responsive header */}
        <div className={cn(
          "flex flex-col gap-4",
          isMobile ? "space-y-3" : "sm:flex-row sm:items-center sm:justify-between sm:gap-4"
        )}>
          <CardTitle className="text-white text-xl">{title}</CardTitle>

          {/* MOBILE FIX: Mobile-optimized header actions */}
          <div className={cn(
            "flex items-center gap-2",
            isMobile ? "flex-col space-y-2 w-full" : "flex-wrap"
          )}>
            {onCreate && showCreateButton && (
              <TouchButton
                onClick={onCreate}
                variant="primary"
                size={isMobile ? "lg" : "md"}
                className={cn("glass-button", isMobile ? "w-full" : "")}
                aria-label={createButtonText || `${t.create} ${title || 'item'}`}
              >
                <Plus className="h-4 w-4 mr-2" aria-hidden="true" />
                {createButtonText || t.create}
              </TouchButton>
            )}

            {onRefresh && (
              <Button
                onClick={() => {
                  // UX FIX: Enhanced refresh feedback
                  const loadingId = uxFeedback.loading(
                    language === 'ar' ? 'جاري تحديث البيانات...' : 'Refreshing data...',
                    { expectedDuration: 3000 }
                  )

                  try {
                    onRefresh()
                    // Note: The parent component should call uxFeedback.dismissLoading when done
                  } catch (error) {
                    uxFeedback.dismissLoading(loadingId, 'error',
                      language === 'ar' ? 'فشل في تحديث البيانات' : 'Failed to refresh data'
                    )
                  }
                }}
                variant="outline"
                className="glass-button"
                disabled={loading}
                aria-label={loading ? 'Refreshing...' : t.refresh}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} aria-hidden="true" />
                {t.refresh}
              </Button>
            )}

            {/* Enhanced Export Button with Custom Handler */}
            {showExportButton && onExport && (
              <div className="relative">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="glass-button">
                      <Download className="h-4 w-4 mr-2" />
                      {t.export}
                      <ChevronDown className="h-4 w-4 ml-2" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem onClick={() => onExport('csv')}>
                      <FileText className="h-4 w-4 mr-2" />
                      {language === 'ar' ? 'تصدير CSV' : 'Export CSV'}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onExport('excel')}>
                      <FileSpreadsheet className="h-4 w-4 mr-2" />
                      {language === 'ar' ? 'تصدير إكسل' : 'Export Excel'}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onExport('pdf')}>
                      <FileImage className="h-4 w-4 mr-2" />
                      {language === 'ar' ? 'تصدير PDF' : 'Export PDF'}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}

            {/* Fallback to Unified Export Button if no custom handler */}
            {showExportButton && !onExport && (
              <UnifiedExportButton
                dataType={(title || 'data').toLowerCase().replace(/\s+/g, '-')}
                data={data}
                className="glass-button"
                variant="outline"
                showAdvanced={true}
                language={language}
              />
            )}

            {onImport && showImportButton && (
              <div>
                <input
                  type="file"
                  id="file-import"
                  className="hidden"
                  accept=".csv,.xlsx,.xls"
                  onChange={handleFileImport}
                />
                <Button
                  onClick={() => document.getElementById('file-import')?.click()}
                  variant="outline"
                  className="glass-button"
                  aria-label={`${t.import} ${title || 'data'} file`}
                >
                  <Upload className="h-4 w-4 mr-2" aria-hidden="true" />
                  {t.import}
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          {onSearchChange && (
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
                <Input
                  placeholder={searchPlaceholder || t.search}
                  value={searchQuery}
                  onChange={(e) => onSearchChange(e.target.value)}
                  className="glass-input pl-10 pr-10"
                  aria-label={searchPlaceholder || t.search}
                  role="searchbox"
                />
                {/* FIXED: Add clear button */}
                {searchQuery && (
                  <button
                    onClick={() => onSearchChange('')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60 hover:text-white/80 transition-colors"
                    aria-label="Clear search"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Filter Toggle */}
          {filters.length > 0 && (
            <Button
              onClick={() => setShowFilters(!showFilters)}
              variant="outline"
              className="glass-button"
              aria-label={showFilters ? `Hide ${t.filter}` : `Show ${t.filter}`}
              aria-expanded={showFilters}
              aria-controls="filter-panel"
            >
              <Filter className="h-4 w-4 mr-2" aria-hidden="true" />
              {t.filter}
            </Button>
          )}
        </div>

        {/* Filters */}
        {showFilters && filters.length > 0 && (
          <div
            id="filter-panel"
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-white/5 rounded-lg"
            role="region"
            aria-label={`${t.filter} ${title || 'data'}`}
          >
            {filters.map((filter) => (
              <div key={filter.key}>
                <label className="text-white text-sm mb-2 block">{filter.label}</label>
                <Select
                  value={activeFilters[filter.key] || 'all'}
                  onValueChange={(value) => {
                    const newValue = value === 'all' ? '' : value
                    onFilterChange?.({ ...activeFilters, [filter.key]: newValue })
                  }}
                >
                  <SelectTrigger className="glass-input">
                    <SelectValue placeholder={`Select ${filter.label}`} />
                  </SelectTrigger>
                  <SelectContent className="glass-card border-white/20">
                    <SelectItem value="all">All</SelectItem>
                    {filter.options.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ))}
          </div>
        )}

        {/* Selection and Bulk Actions */}
        {selectable && selectedItems.length > 0 && (
          <div className="flex items-center justify-between p-3 bg-blue-500/20 rounded-lg">
            <span className="text-white text-sm">
              {selectedItems.length} {t.selected}
            </span>
            <div className="flex items-center gap-2">
              {bulkActions.map((action, actionIndex) => {
                const IconComponent = action.icon
                return (
                  <Button
                    key={actionIndex}
                    onClick={() => action.onClick(selectedItems as unknown as Record<string, unknown>)}
                    variant={action.variant || 'outline'}
                    size="sm"
                    className="glass-button"
                  >
                    <IconComponent className="h-4 w-4 mr-2" />
                    {action.label}
                  </Button>
                )
              })}
              <Button
                onClick={() => onSelectionChange?.([])}
                variant="outline"
                size="sm"
                className="glass-button"
              >
                {t.clearSelection}
              </Button>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {/* UX FIX: Enhanced loading overlay with better feedback */}
        <LoadingOverlay
          isLoading={loading}
          text={t.loading}
          showNetworkStatus={true}
          preserveHeight={true}
          minHeight={400}
          language={language}
        >
          {loading ? (
            // Enhanced skeleton loading state to prevent CLS
            <div className="space-y-4">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/20">
                      {columns.map((column, index) => (
                        <TableHead key={index} className="text-white/80">
                          <div className="h-4 bg-white/10 rounded animate-pulse"></div>
                        </TableHead>
                      ))}
                      {actions.length > 0 && (
                        <TableHead className="text-white/80 w-32">
                          <div className="h-4 bg-white/10 rounded animate-pulse"></div>
                        </TableHead>
                      )}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Array.from({ length: Math.min(pageSize, 5) }).map((_, index) => (
                      <TableRow key={index} className="border-white/10">
                        {columns.map((_, colIndex) => (
                          <TableCell key={colIndex}>
                            <div className="h-4 bg-white/5 rounded animate-pulse"></div>
                          </TableCell>
                        ))}
                        {actions.length > 0 && (
                          <TableCell>
                            <div className="flex gap-1">
                              {actions.slice(0, 3).map((_, actionIndex) => (
                                <div key={actionIndex} className="h-8 w-8 bg-white/5 rounded animate-pulse"></div>
                            ))}
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        ) : data.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-white/60">{t.noData}</p>
          </div>
        ) : (
          <>
            {/* MOBILE FIX: Responsive Table */}
            {isMobile ? (
              // Mobile card layout
              <div className="space-y-4">
                {data.map((item, index) => (
                  <div key={(item as Record<string, unknown>).id as string || index}
                       className="glass-card border-white/20 p-4 space-y-3">
                    {/* Mobile card content */}
                    {columns.map((column) => (
                      <div key={column.key} className="flex justify-between items-start">
                        <span className="text-white/70 text-sm font-medium min-w-0 flex-1">
                          {column.label}
                        </span>
                        <span className="text-white text-sm text-right ml-2 flex-1">
                          {column.render ? (column.render(item) || '-') :
                            (typeof (item as Record<string, unknown>)[column.key] === 'object' && (item as Record<string, unknown>)[column.key] !== null)
                              ? JSON.stringify((item as Record<string, unknown>)[column.key])
                              : String((item as Record<string, unknown>)[column.key] || '-')
                          }
                        </span>
                      </div>
                    ))}

                    {/* Mobile actions */}
                    {actions.length > 0 && (
                      <div className="flex gap-2 pt-2 border-t border-white/10">
                        {actions.slice(0, 2).map((action, actionIndex) => {
                          if (action.show && !action.show(item)) return null
                          const Icon = action.icon
                          return (
                            <TouchButton
                              key={actionIndex}
                              variant="ghost"
                              size="sm"
                              onClick={() => action.onClick(item)}
                              className="flex-1 text-white hover:bg-white/10"
                            >
                              <Icon className="h-4 w-4 mr-1" />
                              {action.label}
                            </TouchButton>
                          )
                        })}
                        {actions.length > 2 && (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <TouchButton variant="ghost" size="sm" className="text-white hover:bg-white/10">
                                <MoreVertical className="h-4 w-4" />
                              </TouchButton>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="glass-card border-white/20">
                              {actions.slice(2).map((action, actionIndex) => {
                                if (action.show && !action.show(item)) return null
                                const Icon = action.icon
                                return (
                                  <DropdownMenuItem
                                    key={actionIndex + 2}
                                    onClick={() => action.onClick(item)}
                                    className="text-white hover:bg-white/10"
                                  >
                                    <Icon className="h-4 w-4 mr-2" />
                                    {action.label}
                                  </DropdownMenuItem>
                                )
                              })}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              // Desktop table layout
              <div className="overflow-x-auto">
                <Table role="table" aria-label={`${title || 'Data'} table`}>
                  <TableHeader>
                    <TableRow className="border-white/20">
                    {selectable && (
                      <TableHead className="w-12">
                        <input
                          type="checkbox"
                          checked={selectedItems.length === data.length && data.length > 0}
                          onChange={handleSelectAll}
                          className="rounded border-white/20"
                          aria-label={`Select all ${title || 'items'}`}
                        />
                      </TableHead>
                    )}
                    {columns.map((column) => (
                      <TableHead
                        key={column.key}
                        className={`text-white ${column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : 'text-left'}`}
                        style={{ width: column.width }}
                      >
                        {column.sortable ? (
                          <Button
                            variant="ghost"
                            onClick={() => handleSort(column.key)}
                            className="text-white hover:text-white hover:bg-white/10 p-0 h-auto font-medium"
                            aria-label={`Sort by ${column.label} ${
                              sortBy === column.key
                                ? (sortOrder === 'asc' ? 'descending' : 'ascending')
                                : 'ascending'
                            }`}
                            aria-sort={
                              sortBy === column.key
                                ? (sortOrder === 'asc' ? 'ascending' : 'descending')
                                : 'none'
                            }
                          >
                            {column.label}
                            {sortBy === column.key && (
                              sortOrder === 'asc' ? (
                                <SortAsc className="h-4 w-4 ml-1" aria-hidden="true" />
                              ) : (
                                <SortDesc className="h-4 w-4 ml-1" aria-hidden="true" />
                              )
                            )}
                          </Button>
                        ) : (
                          column.label
                        )}
                      </TableHead>
                    ))}
                    {actions.length > 0 && (
                      <TableHead className="text-white text-center w-24">{t.actions}</TableHead>
                    )}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.map((item, index) => (
                    <TableRow key={(item as Record<string, unknown>).id as string || index} className="border-white/10 hover:bg-white/5">
                      {selectable && (
                        <TableCell>
                          <input
                            type="checkbox"
                            checked={selectedItems.some(selected => (selected as Record<string, unknown>).id === (item as Record<string, unknown>).id)}
                            onChange={() => handleSelectItem(item)}
                            className="rounded border-white/20"
                          />
                        </TableCell>
                      )}
                      {columns.map((column) => (
                        <TableCell
                          key={column.key}
                          className={`text-white/90 ${column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : 'text-left'}`}
                        >
                          {column.render ? (column.render(item) || '-') :
                            (typeof (item as Record<string, unknown>)[column.key] === 'object' && (item as Record<string, unknown>)[column.key] !== null)
                              ? JSON.stringify((item as Record<string, unknown>)[column.key])
                              : String((item as Record<string, unknown>)[column.key] || '-')
                          }
                        </TableCell>
                      ))}
                      {actions.length > 0 && (
                        <TableCell className="text-center">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-white hover:bg-white/10"
                                aria-label={`Actions for ${
                                  (item as any).name ||
                                  (item as any).title ||
                                  (item as any).firstName ||
                                  'item'
                                }`}
                                aria-haspopup="menu"
                                aria-expanded="false"
                              >
                                <MoreVertical className="h-4 w-4" aria-hidden="true" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="glass-card border-white/20">
                              {actions.map((action, actionIndex) => {
                                if (action.show && !action.show(item)) return null

                                const IconComponent = action.icon

                                return (
                                  <DropdownMenuItem
                                    key={actionIndex}
                                    onClick={() => action.onClick(item)}
                                    className={`text-white ${action.className || ''} ${action.variant === 'destructive' ? 'text-red-400 hover:text-red-300 hover:bg-red-500/10' : 'hover:bg-white/10'}`}
                                  >
                                    <IconComponent className="h-4 w-4 mr-2" />
                                    {action.label}
                                  </DropdownMenuItem>
                                )
                              })}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            )}
            {/* End of responsive table */}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-white/60 text-sm">
                  {t.page} {currentPage} {t.of} {totalPages} ({total} {t.items})
                </div>

                <div className="flex items-center gap-2" role="navigation" aria-label="Pagination">
                  <Button
                    onClick={() => onPageChange?.(currentPage - 1)}
                    disabled={currentPage === 1}
                    variant="outline"
                    size="sm"
                    className="glass-button"
                    aria-label={`${t.previous} page (${currentPage - 1})`}
                  >
                    <ChevronLeft className="h-4 w-4" aria-hidden="true" />
                    {t.previous}
                  </Button>

                  <Button
                    onClick={() => onPageChange?.(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    variant="outline"
                    size="sm"
                    className="glass-button"
                    aria-label={`${t.next} page (${currentPage + 1})`}
                  >
                    {t.next}
                    <ChevronRight className="h-4 w-4" aria-hidden="true" />
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
        </LoadingOverlay>
      </CardContent>
    </Card>
  )
}
