import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { Toaster } from 'react-hot-toast'
import type { AppDispatch, RootState } from './store'
import { verifyToken } from './store/slices/authSlice'
import { fetchNotifications } from './store/slices/notificationSlice'
import { fetchDashboardLayout } from './store/slices/dashboardSlice'
import Layout from './components/Layout'
import ErrorBoundary from './components/ErrorBoundary'
import { ToastProvider } from './contexts/ToastContext'
import { log } from './utils/logger'
import './utils/authSync' // Import auth sync utilities for global access
import { useNavigationServiceInit } from './hooks/useNavigationService' // FIXED: Add navigation service
import Login from './pages/Login'
import Home from './pages/Home'
import HowItWorks from './pages/HowItWorks'
import EmployeeActivation from './pages/EmployeeActivation'
import RoleBasedRouter from './routes/RoleBasedRouter'
import UserManagementTest from './pages/test/UserManagementTest'
// Modal system removed - using Redux instead
import { getPerformanceMonitor } from './utils/performanceMonitor'
// Removed unused imports - components and test utilities cleaned up





function App() {
  const dispatch = useDispatch<AppDispatch>()
  const { isAuthenticated, user, isLoading } = useSelector((state: RootState) => state.auth)
  const [language, setLanguage] = useState<'ar' | 'en'>('ar')
  const [hasTriedTokenVerification, setHasTriedTokenVerification] = useState(false)

  // React-based language and direction management
  useEffect(() => {
    const root = document.documentElement
    const isRTL = language === 'ar'

    // Set document attributes for RTL/LTR support
    root.setAttribute('dir', isRTL ? 'rtl' : 'ltr')
    root.setAttribute('lang', language)

    // Update CSS classes for styling
    root.classList.remove('rtl', 'ltr')
    root.classList.add(isRTL ? 'rtl' : 'ltr')

    // CSS custom properties for theme consistency
    root.style.setProperty('--text-direction', isRTL ? 'rtl' : 'ltr')
    root.style.setProperty('--language', language)
    root.style.setProperty('--font-family', isRTL ? 'var(--font-arabic)' : 'var(--font-latin)')
  }, [language])

  // FIXED: Proper async state management for token verification
  const [verificationState, setVerificationState] = useState<'idle' | 'pending' | 'complete' | 'failed'>('idle')

  useEffect(() => {
    // Only verify if we haven't tried yet and user is not already authenticated
    if (verificationState !== 'idle' || user) return

    // Always attempt verification on app load
    if (!isAuthenticated && verificationState === 'idle') {
      log.debug('app', 'Starting token verification on app load')
      setVerificationState('pending')

      dispatch(verifyToken())
        .unwrap()
        .then(() => {
          log.debug('app', 'Token verification successful')
          setVerificationState('complete')
        })
        .catch((error) => {
          log.debug('app', 'Token verification failed:', error)
          setVerificationState('failed')
        })
    }
  }, [dispatch, isAuthenticated, user, verificationState])

  // Update hasTriedTokenVerification based on verification state
  useEffect(() => {
    if (verificationState === 'complete' || verificationState === 'failed') {
      setHasTriedTokenVerification(true)
    }
  }, [verificationState])

  // REMOVED: All timeout and reload logic that was causing issues
  // The app should handle authentication states naturally without forced reloads

  // Load user-specific data when authenticated (with deduplication)
  useEffect(() => {
    if (isAuthenticated && user?.role?.id) {
      // Check if we've recently fetched data to prevent duplicates
      const lastFetch = sessionStorage.getItem('app_data_last_fetch')
      const now = Date.now()
      const shouldFetch = !lastFetch || (now - parseInt(lastFetch)) > 30000 // 30 seconds

      if (shouldFetch) {
        log.debug('app', 'Loading user-specific data')
        dispatch(fetchNotifications(user.role.id))
        dispatch(fetchDashboardLayout(user.role.id))
        // SECURITY FIX: Use sessionStorage for non-sensitive data instead of localStorage
        sessionStorage.setItem('app_data_last_fetch', now.toString())
      } else {
        log.debug('app', 'Skipping data fetch - recent data available')
      }
    }
  }, [dispatch, isAuthenticated, user?.role?.id]) // FIXED: More specific dependency

  // Initialize performance monitoring
  useEffect(() => {
    const monitor = getPerformanceMonitor()

    // Send metrics to analytics after page load
    const timer = setTimeout(() => {
      monitor.sendMetricsToAnalytics()
    }, 3000)

    return () => clearTimeout(timer)
  }, [])

  // Removed export test functions - cleaned up debugging code

  // Debug logging
  log.debug('app', 'App State', {
    isLoading,
    isAuthenticated,
    hasUser: !!user,
    // SECURITY FIX: Token removed - handled by httpOnly cookies
    hasTriedTokenVerification
  })

  // REMOVED: Inconsistent state logic that was clearing valid tokens
  // Let the natural auth flow handle state consistency

  // Create a wrapper component to check current route
  const AppContent = () => {
    const location = useLocation()
    const isActivationRoute = location.pathname.startsWith('/activate/')

    // FIXED: Initialize navigation service with React Router
    useNavigationServiceInit()

    // For activation routes, skip authentication and show the page
    if (isActivationRoute) {
      // Don't redirect - let the routing system handle it
      return (
        <Routes>
          <Route path="/activate/:token" element={<EmployeeActivation />} />
        </Routes>
      )
    }

    // SIMPLIFIED: Only show loading when explicitly pending
    if (verificationState === 'pending') {
      log.debug('app', 'Showing loading screen for token verification')
      return (
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
          <div className="text-center glass-card p-8 rounded-xl shadow-xl">
            <div className="animate-spin rounded-full h-24 w-24 border-b-2 border-white mx-auto mb-6"></div>
            <p className="text-white text-xl mb-4 font-medium">
              {language === 'ar' ? 'جاري تحميل بيانات المستخدم...' : 'Loading user data...'}
            </p>
            <p className="text-white/70 text-sm">
              {language === 'ar' ? 'التحقق من المصادقة...' : 'Verifying authentication...'}
            </p>
            {/* Loading progress bar */}
            <div className="w-full h-1 bg-white/10 rounded-full mt-6 overflow-hidden">
              <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 animate-pulse"
                   style={{width: '60%'}}></div>
            </div>
          </div>
        </div>
      )
    }

    // Normal authentication flow
    if (!isAuthenticated) {
      return (
        <Routes>
          <Route path="/login" element={<Login language={language} />} />
          <Route path="/how-it-works" element={<HowItWorks language={language} setLanguage={setLanguage} />} />
          <Route path="/home" element={<Home language={language} setLanguage={setLanguage} />} />
          <Route path="/activate/:token" element={<EmployeeActivation />} />
          {/* Test route for development */}
          <Route path="/test/user-management" element={<UserManagementTest />} />
          {/* Redirect all other routes to login for unauthenticated users */}
          <Route path="/*" element={<Login language={language} />} />
        </Routes>
      )
    } else {
      return (
        <Layout language={language} setLanguage={setLanguage}>
          <RoleBasedRouter language={language} />
        </Layout>
      )
    }
  }

  return (
    <ErrorBoundary>
      <ToastProvider>
        <Router>
          <AppContent />

          {/* Chat widgets removed - cleaned up unused components */}
        </Router>

        {/* Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
          style: {
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            color: '#fff',
            borderRadius: '12px',
            fontSize: '14px',
            fontWeight: '500'
          },
          success: {
            iconTheme: {
              primary: '#10b981',
              secondary: '#fff',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
          },
        }}
      />
      </ToastProvider>
    </ErrorBoundary>
  )
}

export default App
